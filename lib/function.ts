import { IAccount } from '@/models/account';
import dayjs from 'dayjs';
import 'dayjs/locale/id';
import customParseFormat from 'dayjs/plugin/customParseFormat';

dayjs.locale('id');
dayjs.extend(customParseFormat);

const formatRp = (value = 0) => {
    try {
        return new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR' }).format(value).replace(',00', '');
    } catch (_) {
        console.log('error formatRp', _);
        return 'Rp 0';
    }
};

const _saveExcelFile = (buffer: any, fileName: string) => {
    import('file-saver').then((module) => {
        if (module?.default) {
            const data = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' });

            module.default.saveAs(data, `${fileName}.xlsx`);
        }
    });
};

const _populateAccount = (list: IAccount[]) => {
    const pdf: any[] = [];

    list.forEach(({ number, name, area, status }, at) => {
        pdf.push([at + 1, number, name, area, status ? 'AKTIF' : 'TIDAK AKTIF']);
    });

    return { pdf, head: ['No', 'No Anggota', 'Nama', 'Area (RT)', 'Status'], excel: list.map(({ number, name, area, status }, at) => ({ No: at + 1, 'No Anggota': number, Nama: name, 'Area (RT)': area, Status: status ? 'AKTIF' : 'TIDAK AKTIF' })) };
};

const _populateTransaction = (list: any[], category: string) => {
    const pdf: any[] = [];
    const types = [
        { value: 'tabungan', label: 'Tabungan' },
        { value: 'wajib', label: 'Simpanan Wajib' },
        { value: 'tarik', label: 'Penarikan Simpanan' },
        { value: 'pokok', label: 'Simpanan Pokok' },
        { value: 'cicilan', label: 'Bayar Cicilan' },
        { value: 'pinjaman', label: 'Ambil Pinjaman' }
    ];

    list.forEach(({ date, type, amount, count }, at) => {
        pdf.push([at + 1, dayjs(date, 'DD-MM-YYYY').format('DD MMMM YYYY'), types.find(({ value }) => value === type)?.label || type, formatRp(amount), formatRp(count)]);
    });

    return {
        pdf,
        head: ['No', 'Tanggal', 'Jenis', 'Nominal', category === 'loan' ? 'Sisa' : 'Saldo'],
        excel: list.map(({ date, type, amount, count }, at) => ({
            No: at + 1,
            Tanggal: dayjs(date, 'DD-MM-YYYY').format('DD MMMM YYYY'),
            Jenis: types.find(({ value }) => value === type)?.label || type,
            Nominal: formatRp(amount),
            [category === 'loan' ? 'Sisa' : 'Saldo']: formatRp(count)
        }))
    };
};

const _populateDonate = (list: any[]) => {
    const pdf: any[] = [];
    const types = [
        { value: 'infaq', label: 'Pemberian Infaq' },
        { value: 'manfaat', label: 'Penggunaan Infaq' }
    ];

    list.forEach(({ date, type, amount, info, count }, at) => {
        pdf.push([at + 1, dayjs(date, 'DD-MM-YYYY').format('DD MMMM YYYY'), types.find(({ value }) => value === type)?.label || type, info, formatRp(amount), formatRp(count)]);
    });

    return {
        pdf,
        head: ['No', 'Tanggal', 'Jenis', 'Keterangan', 'Nominal', 'Saldo'],
        excel: list.map(({ date, type, amount, info, count }, at) => ({
            No: at + 1,
            Tanggal: dayjs(date, 'DD-MM-YYYY').format('DD MMMM YYYY'),
            Jenis: types.find(({ value }) => value === type)?.label || type,
            Keterangan: info,
            Nominal: formatRp(amount),
            Saldo: formatRp(count)
        }))
    };
};

const exportPdf = (list: any[], segment: string, filename?: string) => {
    import('jspdf').then((jsPDF) => {
        import('jspdf-autotable').then(({ autoTable }) => {
            const doc = new jsPDF.default({ orientation: 'p', format: 'a4' });
            let body: any[] = [];
            let head: any[] = [];
            let populated: any;

            switch (segment) {
                case 'account':
                    populated = _populateAccount(list);
                    body = populated?.pdf;
                    head = [populated?.head];

                    break;
                case 'saving':
                    populated = _populateTransaction(list, 'saving');
                    body = populated?.pdf;
                    head = [populated?.head];

                    break;
                case 'loan':
                    populated = _populateTransaction(list, 'loan');
                    body = populated?.pdf;
                    head = [populated?.head];

                    break;
                case 'donate':
                    populated = _populateDonate(list);
                    body = populated?.pdf;
                    head = [populated?.head];

                    break;
            }

            autoTable(doc, { theme: 'grid', head, body });
            doc.save(`${filename ?? segment}.pdf`);
        });
    });
};

const exportExcel = (list: any[], segment: string, filename?: string) => {
    import('xlsx').then((xlsx) => {
        let json: any[] = [];

        switch (segment) {
            case 'account':
                json = _populateAccount(list)?.excel;

                break;
            case 'saving':
                json = _populateTransaction(list, 'saving')?.excel;

                break;
            case 'loan':
                json = _populateTransaction(list, 'loan')?.excel;

                break;
            case 'donate':
                json = _populateDonate(list)?.excel;

                break;
        }

        const worksheet = xlsx.utils.json_to_sheet(json);
        const workbook = { Sheets: { data: worksheet }, SheetNames: ['data'] };
        const excelBuffer = xlsx.write(workbook, { bookType: 'xlsx', type: 'array' });
        _saveExcelFile(excelBuffer, filename ?? segment);
    });
};

export { exportExcel, exportPdf, formatRp };
